/*
 * Requisition Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const REQUISITION_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Requisition-related technical configurations
    // Add functional settings here as needed (form validation rules, API settings, etc.)

    // ===== 2. UI TEXT (Bilingual) =====
    // Requisition UI text organized by component/feature
    ui: {
        // Table configurations
        table: {
            open: {
                containerId: 'open-requisitions-table-container',
                tableId: 'open-requisitions-table',
                title: {
                    en: 'Open Requisitions',
                    fr: 'Demandes ouvertes'
                },
                apiStatusQuery: 'submitted&status=in_progress'
            },
            closed: {
                containerId: 'closed-requisitions-table-container',
                tableId: 'closed-requisitions-table',
                title: {
                    en: 'Closed Requisitions',
                    fr: 'Demandes fermées'
                },
                apiStatusQuery: 'complete'
            },

            // Column configurations
            columns: {
                requisition: {
                    en: 'Requisition',
                    fr: 'Demande'
                },
                lab: {
                    en: 'Lab',
                    fr: 'Laboratoire'
                },
                // dateModified uses GLOBAL_CONFIGS.ui.table.columns.dateModified
            },

            // Uses GLOBAL_CONFIGS.ui.table.messageTemplates with entity-specific terms
            entityTerms: {
                en: 'requisitions',
                fr: 'demandes'
            }
        }

        // Add more UI sections here as needed (forms, navigation, etc.)
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Requisition-specific messages organized by type
    // Add requisition-specific messages here when needed (creation, update, status change messages, etc.)
    messages: {
        // Add requisition-specific message sections here as needed:
        // errors: { ... },
        // success: { ... },
        // warnings: { ... },
        // info: { ... }
    }
};
