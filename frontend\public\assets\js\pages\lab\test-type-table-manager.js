/*
 * Test Type Table Manager
 * Editable table for managing test types with bulk operations
 * Uses BaseTable for standardized WET-BOEW DataTables implementation
 */
import { TestApi } from '../../core/services/test-api.js';
import { BulkCheckboxTable } from '../../core/helpers/base-table.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { escapeHtml, formatDate } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    test: TEST_CONFIGS
};

class TestTypeTableManager extends BulkCheckboxTable {
    constructor() {
        super({
            containerId: TEST_CONFIGS.ui.testTypeTable.containerId,
            tableId: TEST_CONFIGS.ui.testTypeTable.tableId,
            entityConfig: {
                ...TEST_CONFIGS.ui.testTypeTable,
                ...TEST_CONFIGS.ui.testTypeTable.manage,
                entityTerms: TEST_CONFIGS.ui.testTypeTable.entityTerms
            },
            messageConfigMaps: {
                global: GLOBAL_CONFIGS,
                test: TEST_CONFIGS
            }
        });
        this.userInfo = null;
    }

    // Fetch test types data using shared helper
    async fetchData() {
        return await fetchTestTypesForLab(this.userInfo);
    }

    // Get table headers configuration
    getTableHeaders() {
        const columns = TEST_CONFIGS.ui.testTypeTable.columns;
        return {
            testName: getLocalizedText({ message: columns.name }, 'message'),
            testDescription: getLocalizedText({ message: columns.description }, 'message'),
            dateUpdated: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.dateModified }, 'message'),
            active: getLocalizedText({ message: columns.status }, 'message')
        };
    }

    // Build table rows from test types data
    buildTableRows(testTypes) {
        return testTypes.map(testType => {
            const testId = testType.test_type_id;
            const testName = escapeHtml(testType.name || '');
            const testDescription = escapeHtml(testType.description || '');
            const dateUpdated = formatDate(testType.updated_at || testType.created_at);
            const isActive = testType.is_active;
            const checkedAttr = isActive ? 'checked' : '';

            const activeCheckbox = `
                <div class="checkbox gc-chckbxrdio">
                    <input type="checkbox"
                           id="test-active-${testId}"
                           class="test-status-checkbox"
                           data-test-id="${testId}"
                           data-original-state="${isActive}"
                           ${checkedAttr}>
                    <label for="test-active-${testId}">
                        <span class="wb-inv">Toggle active status for ${testName}</span>
                    </label>
                </div>`;

            return `<tr>
                <td>${testName}</td>
                <td>${testDescription}</td>
                <td>${dateUpdated}</td>
                <td>${activeCheckbox}</td>
            </tr>`;
        }).join('');
    }

    // Override table options for management table
    getTableOptions() {
        return {
            order: [[2, "desc"]], // Sort by date updated descending
            columnDefs: [{ orderable: false, targets: [3] }] // Disable sorting on checkbox column
        };
    }

    // Override buildTable to add bulk actions
    buildTable(data) {
        const tableHtml = super.buildTable(data);
        const bulkActionsHtml = this.buildBulkActionsHtml();
        return tableHtml + bulkActionsHtml;
    }

    // Build bulk actions HTML
    buildBulkActionsHtml() {
        const saveChangesText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
        const cancelText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.cancel }, 'message');

        return `
            <!-- Bulk Action Buttons - Following GC Web Standards pattern -->
            <div class="form-group mrgn-tp-lg mrgn-bttm-xl" id="bulk-actions-container">
                <button type="button" class="btn btn-primary mrgn-rght-md" id="save-changes-btn">
                    <span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>
                    ${saveChangesText}
                </button>
                <button type="button" class="btn btn-default" id="cancel-changes-btn">
                    <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                    ${cancelText}
                </button>
            </div>
        `;
    }

    // Initialize event handlers for editable functionality
    initializeEventHandlers() {
        const self = this;

        // Handle checkbox changes
        $(document).off('change', '.test-status-checkbox').on('change', '.test-status-checkbox', function() {
            self.handleCheckboxChange($(this));
        });

        // Handle save changes
        $(document).off('click', '#save-changes-btn').on('click', '#save-changes-btn', function() {
            self.handleSaveChanges();
        });

        // Handle cancel changes
        $(document).off('click', '#cancel-changes-btn').on('click', '#cancel-changes-btn', function() {
            self.handleCancelChanges();
        });

        // Initialize button states - buttons are always enabled in old pattern
        this.trackChanges(false);
    }

    // Reset save button to default state
    resetSaveButton() {
        const $saveBtn = $('#save-changes-btn');
        const saveText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
        $saveBtn.prop('disabled', false).html(`<span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span> ${saveText}`);
    }

    // Handle individual checkbox change
    handleCheckboxChange($checkbox) {
        const originalState = $checkbox.data('original-state');
        const currentState = $checkbox.is(':checked');

        // Update change tracking
        if (originalState !== currentState) {
            this.trackChanges(true);
        } else {
            // Check if any other checkboxes have changes
            this.trackChanges(this.hasAnyChanges());
        }
    }

    // Check if any checkboxes have changes
    hasAnyChanges() {
        let hasChanges = false;
        $('.test-status-checkbox').each(function() {
            const $checkbox = $(this);
            const originalState = $checkbox.data('original-state');
            const currentState = $checkbox.is(':checked');
            if (originalState !== currentState) {
                hasChanges = true;
                return false; // Break out of each loop
            }
        });
        return hasChanges;
    }

    // Update bulk action button states (following old code pattern)
    toggleBulkActions() {
        const $saveBtn = $('#save-changes-btn');
        const $cancelBtn = $('#cancel-changes-btn');

        // Buttons are always enabled in the old pattern
        $saveBtn.prop('disabled', false);
        $cancelBtn.prop('disabled', false);
    }

    // Collect all changes for batch update
    collectChanges() {
        const updates = [];
        $('.test-status-checkbox').each(function() {
            const $checkbox = $(this);
            const testId = $checkbox.data('test-id');
            const originalState = $checkbox.data('original-state');
            const currentState = $checkbox.is(':checked');

            if (originalState !== currentState) {
                updates.push({
                    test_type_id: testId,
                    is_active: currentState
                });
            }
        });
        return updates;
    }

    // Handle save changes action
    async handleSaveChanges() {
        try {
            const updates = this.collectChanges();

            if (updates.length === 0) {
                showMessage('#page-success-container', 'global.messages.info.common', 'noChangesToSave', MESSAGE_CONFIG_MAPS);
                return;
            }

            // Button disable to prevent double-clicks
            const $saveBtn = $('#save-changes-btn');
            $saveBtn.prop('disabled', true);

            // Perform batch update - convert jQuery promise to native promise
            const result = await new Promise((resolve, reject) => {
                TestApi.batchUpdateTestTypes(updates)
                    .done(resolve)
                    .fail(reject);
            });

            // Handle success
            this.handleSaveSuccess(result);

        } catch (error) {
            console.error('Bulk update failed:', error);
            this.handleSaveError(error);
        }
    }

    // Handle successful save
    handleSaveSuccess() {
        // Update original states to current states
        $('.test-status-checkbox').each(function() {
            const $checkbox = $(this);
            const currentState = $checkbox.is(':checked');
            $checkbox.data('original-state', currentState);
        });

        // Reset change tracking
        this.trackChanges(false);
        this.resetSaveButton();

        // Show success message with entity term
        const entityTerm = getLocalizedText({ message: TEST_CONFIGS.ui.testTypeTable.entityTerms }, 'message');
        showMessage('#page-success-container', 'global.messages.success.templates', 'entityUpdated', MESSAGE_CONFIG_MAPS, {
            entity: entityTerm
        });
    }

    // Handle save error
    handleSaveError() {
        this.resetSaveButton();

        // Show error message
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }

    // Handle cancel changes
    handleCancelChanges() {
        // Revert all checkboxes to original states
        $('.test-status-checkbox').each(function() {
            const $checkbox = $(this);
            const originalState = $checkbox.data('original-state');
            $checkbox.prop('checked', originalState);
        });

        // Show cancelled message
        showMessage('#page-success-container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);

        // Reset change tracking (buttons stay visible and enabled)
        this.trackChanges(false);
    }
}

// Export singleton instance
export const testTypeTableManager = new TestTypeTableManager();
