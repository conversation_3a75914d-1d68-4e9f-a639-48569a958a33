/*
 * Format Helpers
 * Data formatting and conversion utilities used across multiple components
 */
import { getLocalizedRoleName, getLocalizedText } from '../i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

// Escape HTML to prevent XSS attacks
export function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Format date for display with optional format specification
export function formatDate(dateString, format = 'default') {
    if (!dateString) {
        return formatNotAvailable();
    }

    try {
        const date = new Date(dateString);
        if (format === 'en-CA') {
            return date.toLocaleDateString('en-CA'); // YYYY-MM-DD format
        }
        return date.toLocaleDateString(); // Default locale format
    } catch (error) {
        return formatNotAvailable();
    }
}

// Get localized role display name using existing i18n helper
export function getRoleDisplayName(roleName) {
    return getLocalizedRoleName(roleName, GLOBAL_CONFIGS.ui.roles);
}

// Format status display text (active/inactive) with localization
export function formatStatusDisplay(isActive, statusLabels) {
    const label = isActive ? statusLabels.active : statusLabels.inactive;
    return getLocalizedText({ message: label }, 'message');
}

// Format "not available" text with localization
export function formatNotAvailable() {
    return getLocalizedText({ message: GLOBAL_CONFIGS.ui.common.notAvailable }, 'message');
}
