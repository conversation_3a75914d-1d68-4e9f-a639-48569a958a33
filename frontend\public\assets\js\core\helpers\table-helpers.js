/*
 * Table Helpers
 * Table-specific utilities for WET-BOEW integration and message handling
 */
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { GLOBAL_CONFIGS } from '../config/global-configs.js';

// Initialize WET-BOEW table plugin with proper event handling
export async function initializeWETTable(tableId) {
    return new Promise((resolve) => {
        const $table = $(`#${tableId}`);
        if ($table.length === 0) {
            resolve();
            return;
        }

        $table.one('wb-ready.wb-tables', () => resolve());

        requestAnimationFrame(() => {
            setTimeout(() => {
                $table.trigger('wb-init.wb-tables');
                setTimeout(() => resolve(), 1000); // Fallback
            }, 100);
        });
    });
}

// Build table message configuration for WET-BOEW with entity-specific terms
export function buildTableMessageConfig(entityTerms) {
    const entityTerm = getLocalizedText({ message: entityTerms }, 'message');
    const templates = GLOBAL_CONFIGS.ui.table.messageTemplates;

    return {
        all: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.all }, 'message'),
        emptyTable: getLocalizedText({ message: templates.emptyTable }, 'message').replace('{entity}', entityTerm),
        search: getLocalizedText({ message: templates.search }, 'message').replace('{entity}', entityTerm),
        lengthMenu: getLocalizedText({ message: templates.lengthMenu }, 'message').replace('{entity}', entityTerm),
        info: getLocalizedText({ message: templates.info }, 'message').replace('{entity}', entityTerm)
    };
}

// Build standard WET-BOEW table HTML structure
export function buildTableHtml(options) {
    const {
        tableId,
        headerCells,
        tableRows,
        messageConfig,
        tableOptions = {}
    } = options;

    // Default WET-BOEW table configuration
    const defaultOptions = {
        ordering: true,
        order: [[0, "asc"]],
        paging: true,
        pageLength: 10,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, messageConfig.all]],
        info: true,
        searching: true,
        columnDefs: [],
        language: {
            emptyTable: messageConfig.emptyTable,
            search: messageConfig.search,
            lengthMenu: messageConfig.lengthMenu,
            info: messageConfig.info
        }
    };

    // Merge with custom options
    const finalOptions = { ...defaultOptions, ...tableOptions };
    const dataWbTables = JSON.stringify(finalOptions);

    return `
        <div class="table-responsive">
            <table id="${tableId}" class="table table-striped table-hover wb-tables"
                data-wb-tables='${dataWbTables}'>
                <thead><tr>${headerCells}</tr></thead>
                <tbody>${tableRows}</tbody>
            </table>
        </div>
    `;
}

// Build standard empty table message
export function buildEmptyTableMessage(entityTerms) {
    const entityTerm = getLocalizedText({ message: entityTerms }, 'message');
    const emptyTableTemplate = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable }, 'message');
    const emptyTableText = emptyTableTemplate.replace('{entity}', entityTerm);

    return `<div class="alert alert-info"><p>${emptyTableText}</p></div>`;
}
