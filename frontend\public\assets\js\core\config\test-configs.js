/*
 * Test Configuration
 *
 * 1. FUNCTIONAL SETTINGS - Global technical configurations (form lengths, navigation delays, etc.)
 * 2. UI TEXT - Global UI text with bilingual support (common buttons, navigation, etc.)
 * 3. MESSAGES - Cross-cutting messages organized by type and category
 * 
 */

export const TEST_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Test-specific technical configurations
    // Form lengths and navigation delays use GLOBAL_CONFIGS standards

    // ===== 2. UI TEXT (Bilingual) =====
    // Test UI text organized by component/feature
    ui: {
        // Test type table configurations
        testTypeTable: {
            // Basic table configuration
            containerId: 'test-types-table-container',
            tableId: 'test-types-table',
            title: {
                en: 'Test Types',
                fr: 'Types de tests'
            },

            // Column headers
            columns: {
                name: {
                    en: 'Test Name',
                    fr: 'Nom du test'
                },
                description: {
                    en: 'Test Description',
                    fr: 'Description du test'
                },
                // dateModified uses GLOBAL_CONFIGS.ui.table.columns.dateModified
                status: {
                    en: 'Status',
                    fr: 'Statut'
                }
            },

            // Status display labels
            statusLabels: {
                active: { en: 'Active', fr: 'Actif' },
                inactive: { en: 'Inactive', fr: 'Inactif' }
            },

            // Management page specific configuration
            manage: {
                addButton: {
                    text: {
                        en: 'Add New Test',
                        fr: 'Ajouter un nouveau test'
                    },
                    urlPattern: '/{lang}/create-lab-test.html'  // Use {lang} placeholder for dynamic language
                }
                // bulkActions use GLOBAL_CONFIGS.ui.buttons
            },

            // Uses GLOBAL_CONFIGS.ui.table.messageTemplates with entity-specific terms
            entityTerms: {
                en: 'test types',
                fr: 'types de tests'
            }
        },

        // Field terms for global template substitution (form validation messages)
        fieldTerms: {
            name: {
                en: 'test name',
                fr: 'nom du test'
            },
            description: {
                en: 'test description',
                fr: 'description du test'
            }
        }
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Test-specific messages that cannot use global templates
    // Most messages use global templates:
    // - Required fields: global.messages.errors.templates.fieldRequired (uses {field} placeholder)
    // - Length validation: global.messages.errors.templates.fieldTooLong (uses {field} placeholder)
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Duplicate names: global.messages.warnings.templates.duplicateName (uses {entity} placeholder)
    // - Cancelled operations: global.messages.info.common.cancelled
    messages: {
        // errors: { ... },
        // success: { ... },
        // warnings: { ... },
        // info: { ... }
    }
};
