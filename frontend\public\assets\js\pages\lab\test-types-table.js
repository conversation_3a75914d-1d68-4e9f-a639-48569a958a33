/*
 * Test Types Table
 * Read-only table for displaying test types in lab information page
 * Uses BaseTable for standardized WET-BOEW DataTables implementation
 */
import { ReadOnlyTable } from '../../core/helpers/base-table.js';
import { escapeHtml, formatDate, formatStatusDisplay } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

class TestTypesTable extends ReadOnlyTable {
    constructor() {
        super({
            containerId: TEST_CONFIGS.ui.testTypeTable.containerId,
            tableId: TEST_CONFIGS.ui.testTypeTable.tableId,
            entityConfig: {
                ...TEST_CONFIGS.ui.testTypeTable,
                entityTerms: TEST_CONFIGS.ui.testTypeTable.entityTerms
            },
            messageConfigMaps: {
                global: GLOBAL_CONFIGS,
                test: TEST_CONFIGS
            }
        });
        this.userInfo = null;
    }

    // Fetch test types data using shared helper
    async fetchData() {
        return await fetchTestTypesForLab(this.userInfo);
    }

    // Get table headers configuration
    getTableHeaders() {
        const columns = TEST_CONFIGS.ui.testTypeTable.columns;
        return {
            name: getLocalizedText({ message: columns.name }, 'message'),
            description: getLocalizedText({ message: columns.description }, 'message'),
            dateUpdated: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.dateModified }, 'message'),
            status: getLocalizedText({ message: columns.status }, 'message')
        };
    }

    // Build table rows from test types data
    buildTableRows(testTypesData) {
        return testTypesData.map(testType => `
            <tr>
                <td>${escapeHtml(testType.name || '')}</td>
                <td>${escapeHtml(testType.description || '')}</td>
                <td>${formatDate(testType.updated_at || testType.created_at)}</td>
                <td>${formatStatusDisplay(testType.is_active, TEST_CONFIGS.ui.testTypeTable.statusLabels)}</td>
            </tr>
        `).join('');
    }
}

// Create and export singleton instance
export const testTypesTable = new TestTypesTable();
