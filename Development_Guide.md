# Sed-LIMS Frontend Development Guide

> **Purpose**: AI Assistant reference for Sed-LIMS frontend development
> **Last Updated**: 2025-01-25

## 🎯 QUICK REFERENCE

### Essential Rules
- **JavaScript**: ES6 modules + jQuery (GCWeb dependency)
- **UI Framework**: GCWeb/WET-BOEW (Government of Canada standards)
- **Bilingual**: EN/FR support mandatory
- **Comments**: English only
- **AI Response Language**: Chinese (Simplified)

### Project Architecture
```
frontend/
├── public/assets/js/
│   ├── core/                    # Shared functionality
│   │   ├── auth/               # Authentication & authorization
│   │   ├── services/           # API service layer
│   │   ├── config/             # Configuration files
│   │   ├── i18n/               # Internationalization
│   │   └── helpers/            # Utility functions
│   └── pages/                   # Page-specific code
│       ├── auth/               # Authentication pages
│       ├── home/               # Home page
│       ├── account/            # Account management
│       ├── lab/                # Laboratory management
│       └── requisition/        # Requisition management
└── server.js                  # Development server
```

### Development Setup
```bash
# Prerequisites: Node.js 18+, Backend API on localhost:8000
cd frontend/
npm install
npm run dev        # Start development server (localhost:3000)

# Environment Variables (injected by server.js)
window.ENV_BACKEND_API_URL  # Backend API URL
```

## 🔧 CORE PATTERNS

### JavaScript Patterns
```javascript
// DOM Ready Pattern
$(document).ready(function() {
    initializePage();
});

// DOM Operations (jQuery required for GCWeb compatibility)
$('#element').addClass('active');
$(document).on('click', '.button', handler);

// Error Handling (async/await + try/catch)
async function initializePage() {
    try {
        const data = await ApiService.getData();
        // Handle success
    } catch (error) {
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// AVOID: Vanilla JS DOM methods (breaks GCWeb compatibility)
// ❌ document.getElementById()
// ❌ element.addEventListener()
```

### HTML Template Requirements
For authenticated pages, include the authentication middleware script:
```html
<!-- Authentication Middleware (required for authenticated pages) -->
<script type="module" src="/assets/js/core/auth/authentication.js"></script>
<!-- Page-specific JavaScript -->
<script type="module" src="/assets/js/pages/your-page/your-page.js"></script>
```

### Standard Page Template
```javascript
// Standard imports for authenticated pages
import { showMessage } from '../../core/helpers/message-helpers.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';
import { ENTITY_CONFIGS } from '../../core/config/entity-configs.js'; // If needed

// Message config maps (required for showMessage)
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    entity: ENTITY_CONFIGS  // Add entity configs as needed
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN, // Optional: specific role required
    onAuthSuccess: initializePageName,                         // Function to call on successful auth
    onAuthFailure: showAccessDeniedError                       // Optional: custom error handler
};

// Page initialization (called by authentication middleware after successful auth)
async function initializePageName(userInfo) {
    try {
        // Page-specific logic here
        await loadPageData(userInfo);
        setupEventHandlers();
    } catch (error) {
        console.error('Page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

async function loadPageData(userInfo) {
    // API calls and data loading
}

function setupEventHandlers() {
    // Event listener setup
}

// Custom access denied handler (optional)
function showAccessDeniedError() {
    showMessage('#wb-cont', 'auth.messages.errors', 'accessDenied', MESSAGE_CONFIG_MAPS);

    // Redirect to home page after showing error
    // Access denied: 3s delay (user operation feedback - allows reading error message)
    setTimeout(() => {
        redirectToPageByKey('home');
    }, GLOBAL_CONFIGS.navigation.redirectDelay);
}
```

### Authentication Configuration
- `requiredRole` (optional): Specific role required to access the page
- `onAuthSuccess` (required): Function to call after successful authentication
- `onAuthFailure` (optional): Custom error handler for authentication failures

### Available Roles
- `GLOBAL_CONFIGS.application.roles.SCIENTIST`
- `GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL`
- `GLOBAL_CONFIGS.application.roles.LAB_ADMIN`
```
## 💬 MESSAGING SYSTEM

### Entity vs Field Concepts

**Important**: Understand the distinction between Entity and Field concepts:

- **Entity**: Business entities (requisition, sample, test type) - database entities with CRUD operations
- **Field**: Form input fields (Test name, Test description) - user input validation

**Placeholder Usage**:
- Entity operations use `{entity}` placeholder with entityTerms
- Field validation uses `{field}` placeholder with fieldTerms

### Core Message Patterns
```javascript
// Basic message display
showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);

// Template messages with placeholders
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#page-success-container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm  // "test types created successfully!"
});

// Field validation messages
const fieldTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.fieldTerms.name }, 'message');
showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm  // "Test name is required."
});

// Length validation
showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm,
    maxLength: GLOBAL_CONFIGS.form.standardLengths.shortName
});
```

### Message Rules
- **NEVER use `alert()`** - Use GC-compliant displays only
- **Page level only** - All messages below page heading
- **Auto-scroll** - User scrolled to top automatically
- **Bilingual** - All messages support EN/FR
- **Types**: success (auto-dismiss), danger (persistent), warning (auto-dismiss), info (auto-dismiss)

### Message Types & Behavior

#### 🚨 DANGER (Persistent - User must dismiss)
**Critical errors requiring immediate attention:**
- Server/API failures
- Authentication/authorization failures
- Required field validation errors
- System errors blocking progress

```javascript
showMessage('#container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
showMessage('#container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);
```

#### ⚠️ WARNING (Auto-dismiss 3s)
**Important but non-blocking issues:**
- Duplicate name conflicts
- Session expiration warnings
- Non-critical validation issues

```javascript
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});
```

#### ✅ SUCCESS (Auto-dismiss 3s)
**Successful operations:**
- CRUD operations completed
- Form submissions successful
- Bulk operations finished

```javascript
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
showMessage('#container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});
```

#### ℹ️ INFO (Auto-dismiss 3s)
**General notifications:**
- User-initiated cancellations
- Status updates
- General feedback

```javascript
showMessage('#container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);
```
## 🔧 CONFIGURATION SYSTEM

### Configuration Files
- **global-configs.js**: App-wide settings (roles, API, common UI, message templates)
- **auth-configs.js**: Authentication (login errors, AAD messages, access control)
- **test-configs.js**: Test management configurations
- **requisition-configs.js**: Requisition management configurations

### Standard Config Structure
All configs follow this three-section pattern:

```javascript
export const ENTITY_CONFIGS = {
    // ===== FUNCTIONAL SETTINGS =====
    form: { maxLength: 50 },
    api: { timeout: 30000 },
    navigation: { redirectDelay: 3000 },

    // ===== UI TEXT (Bilingual) =====
    ui: {
        table: {
            entityTerms: { en: 'items', fr: 'éléments' }
        },
        fieldTerms: {
            name: { en: 'Name', fr: 'Nom' },
            description: { en: 'Description', fr: 'Description' }
        }
    },

    // ===== MESSAGES (Bilingual) =====
    messages: {
        // Most messages now use global templates
        // Add entity-specific messages here only when needed
    }
};
```

### Key Global Configurations
```javascript
// Application roles
GLOBAL_CONFIGS.application.roles.LAB_ADMIN
GLOBAL_CONFIGS.application.roles.SCIENTIST
GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL

// API configuration
GLOBAL_CONFIGS.api.baseUrl

// Form validation standards
GLOBAL_CONFIGS.form.standardLengths.shortName      // 50
GLOBAL_CONFIGS.form.standardLengths.description    // 200

// Message templates (use with entity placeholders)
GLOBAL_CONFIGS.messages.success.templates.entityCreated
GLOBAL_CONFIGS.messages.errors.templates.fieldRequired
GLOBAL_CONFIGS.messages.errors.templates.fieldTooLong
GLOBAL_CONFIGS.messages.warnings.templates.duplicateName

// Table templates (use with entity terms)
GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable
GLOBAL_CONFIGS.ui.table.messageTemplates.search
```

### Message Path Examples
```javascript
// Authentication messages
showMessage('#container', 'auth.messages.errors', 'loginFailed', MESSAGE_CONFIG_MAPS);

// Global server errors
showMessage('#container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);

// Template messages with placeholders
const entityTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.table.entityTerms }, 'message');
const fieldTerm = getLocalizedText({ message: ENTITY_CONFIGS.ui.fieldTerms.name }, 'message');

// Entity operations
showMessage('#container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm  // "test types created successfully!"
});

// Field validation
showMessage('#container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm  // "Test name is required."
});

// Length validation
showMessage('#container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
    field: fieldTerm,
    maxLength: GLOBAL_CONFIGS.form.standardLengths.shortName
});

// Duplicate warnings
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS, {
    entity: entityTerm
});

// Common info messages
showMessage('#container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);
```

### New Global Configuration Usage
```javascript
// Application roles
if (userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN) { ... }

// API configuration
fetch(`${GLOBAL_CONFIGS.api.baseUrl}/endpoint`)

// Common UI text
const saveText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.buttons.save }, 'message');
const roleText = getLocalizedText({ message: GLOBAL_CONFIGS.ui.roles[roleName] }, 'message');

// Form validation with dynamic lengths
if (name.length > GLOBAL_CONFIGS.form.standardLengths.shortName) { ... }
```

### Config Best Practices
- **Verify before deletion**: Search codebase for references
- **Use correct paths**: `auth.messages.errors`, `global.messages.errors.server`
- **Update consistently**: When restructuring, update ALL references
- **Prefer templates**: Use global templates over entity-specific messages

### Text Localization
```javascript
// ✅ CORRECT - Always use localized text
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
const text = getLocalizedText(GLOBAL_CONFIGS.ui.common.loading, 'message');

// ❌ WRONG - Never hardcode text
const text = 'Loading...';
```

## 🎨 GC WEB STANDARDS

### Form Components
```html
<!-- Checkboxes/Radio (ALWAYS use gc-chckbxrdio) -->
<div class="checkbox gc-chckbxrdio">
    <input type="checkbox" id="test-active">
    <label for="test-active">Active</label>
</div>

<!-- Field labels -->
<span class="field-name">Test Name</span>
<strong class="required">(required)</strong>
```

### Button Patterns
```javascript
// Simple disable pattern (avoid complex loading states)
button.disabled = true;
try {
    await performAction();
    // Stay disabled on success
} catch (error) {
    button.disabled = false; // Re-enable on error
}
```

## 🔄 NAVIGATION PATTERNS

### Redirect Delays
```javascript
// Immediate (0ms) - Authentication/security
redirectToPageByKey('home', 0);        // Login success
redirectToPageByKey('login', 0);       // Auth failure

// Default (3s) - User operations (allows message reading)
redirectToPageByKey('manageTestTypes'); // Uses default 3s delay
redirectToPageByKey('home');           // Uses default 3s delay

// Custom delays (rare, must be justified)
redirectToPageByKey('home', 5000);     // Custom with reason
```

### Rules
- **Immediate (0ms)**: Authentication, access control, security
- **Default (3s)**: User operations with feedback messages
- **Custom**: Special cases only, document reasoning

## 📚 ESSENTIAL IMPORTS

```javascript
// Core functionality
import { showMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';

// Core configs (always needed)
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Entity configs (as needed)
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';

// API services
import { TestApi } from '../../core/services/test-api.js';
import { UserApi } from '../../core/services/user-api.js';
import { RequisitionApi } from '../../core/services/requisition-api.js';

// Message config maps (required for showMessage)
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS,           // Add as needed
    requisition: REQUISITION_CONFIGS  // Add as needed
};
```

## 🔍 TROUBLESHOOTING

### Common Issues
```javascript
// Issue: "Message configuration not found"
// ❌ Wrong path
showMessage('#container', 'form', 'duplicateName', MESSAGE_CONFIG_MAPS);
// ✅ Correct path
showMessage('#container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS);

// Issue: Config not found in MESSAGE_CONFIG_MAPS
// Solution: Ensure config is imported and added to maps
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS  // ← Make sure this is imported
};
```

## 📝 CREATING NEW CONFIGURATION FILES

### Configuration File Template
When creating new entity configurations, copy this template and modify as needed:

```javascript
/*
 * [Entity Name] Configuration
 * See Development_Guide.md for structure documentation and examples
 */

export const ENTITY_CONFIGS = {
    // ===== 1. FUNCTIONAL SETTINGS =====
    // Technical configurations (not user-facing)

    form: {
        // Example: maxNameLength: 50, requiredFields: ['name', 'email']
    },

    table: {
        // Example: defaultPageSize: 10, sortColumn: 'name'
    },

    navigation: {
        // Example: redirectDelay: 3000, defaultRoute: '/dashboard'
    },

    api: {
        // Example: entitySpecificEndpoint: '/custom-endpoint'
        // Note: Common API settings (baseUrl, timeout) are in GLOBAL_CONFIGS.api
    },

    // ===== 2. UI TEXT (Bilingual) =====
    // User-facing text organized by component
    ui: {
        table: {
            title: { en: 'Entity Management', fr: 'Gestion des entités' },
            columns: {
                name: { en: 'Name', fr: 'Nom' },
                status: { en: 'Status', fr: 'Statut' }
                // Common columns (dateModified, active) use GLOBAL_CONFIGS.ui.table.columns
            },
            actions: {
                add: { en: 'Add New', fr: 'Ajouter nouveau' }
                // Common actions use GLOBAL_CONFIGS.ui.buttons
            },
            // Table messages use GLOBAL_CONFIGS.ui.table.messageTemplates with entity-specific terms
            entityTerms: {
                en: 'items',
                fr: 'éléments'
            }
        },

        // Field terms for global template substitution
        fieldTerms: {
            name: {
                en: 'Name',
                fr: 'Nom'
            },
            description: {
                en: 'Description',
                fr: 'Description'
            }
        }
    },

    // ===== 3. MESSAGES (Bilingual) =====
    // Entity-specific messages that cannot use global templates
    // Most messages now use global templates:
    // - Required fields: global.messages.errors.templates.fieldRequired (uses {field} placeholder)
    // - Length validation: global.messages.errors.templates.fieldTooLong (uses {field} placeholder)
    // - CRUD operations: global.messages.success/errors.templates.entity* (uses {entity} placeholder)
    // - Duplicate names: global.messages.warnings.templates.duplicateName (uses {entity} placeholder)
    // - Cancelled operations: global.messages.info.common.cancelled
    messages: {
        // Add entity-specific message sections here as needed:
        // errors: { ... },
        // success: { ... },
        // warnings: { ... },
        // info: { ... }
    }
};
```

### Step-by-Step Creation Guide

1. **Copy the template above**
2. **Rename the export**: `ENTITY_CONFIGS` → `YOUR_ENTITY_CONFIGS`
3. **Update functional settings**: Add entity-specific technical configurations
4. **Define UI text**:
   - Update table title and columns
   - Define entityTerms for table messages
   - Define fieldTerms for validation messages
5. **Add entity-specific messages**: Only if global templates are insufficient
6. **Import in your pages**: Add to MESSAGE_CONFIG_MAPS

### Configuration Best Practices

- **Follow the three-section structure**: Functional Settings → UI Text → Messages
- **Use Entity vs Field concepts correctly**:
  - entityTerms for business entities (e.g., "test types")
  - fieldTerms for form fields (e.g., "Test name")
- **Prefer global templates**: Use global message templates when possible
- **Bilingual support**: Always provide both EN and FR text
- **Consistent naming**: Use clear, descriptive configuration keys

## 📊 TABLE IMPLEMENTATION STANDARDS

### Table Architecture Pattern
All tables follow a standardized architecture with two complementary approaches for different use cases.

#### Table Construction Approaches

**1. Object-Oriented Approach (Recommended for Complex Tables)**:
- Use `BaseTable` class hierarchy for complex tables with state management
- Provides inheritance, lifecycle management, and standardized error handling
- Best for tables with editing, bulk operations, or complex interactions

**2. Functional Approach (For Simple Tables)**:
- Use `buildTableHtml()` helper function for simple, stateless tables
- Direct function calls without class inheritance
- Best for simple display tables or custom implementations

#### Base Table Classes

**Available Base Classes**:
- `ReadOnlyTable` - For display-only tables (highly flexible)
- `BulkCheckboxTable` - For tables with bulk checkbox operations
- `InlineEditTable` - For row-by-row inline editing (future)
- `FormModalTable` - For popup form editing (future)

#### Object-Oriented Approach (Complex Tables)

```javascript
/*
 * [Entity] Table (e.g., StaffTable for staff display)
 * Uses BaseTable for standardized WET-BOEW DataTables implementation
 */
import { ReadOnlyTable } from '../../core/helpers/base-table.js';
import { [EntityApi] } from '../../core/services/[entity]-api.js';
import { escapeHtml, formatDate } from '../../core/helpers/format-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { [ENTITY]_CONFIGS } from '../../core/config/[entity]-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

class [Entity]Table extends ReadOnlyTable {
    constructor() {
        super({
            containerId: '[entity]-table-container',
            tableId: '[entity]-table',
            entityConfig: [ENTITY]_CONFIGS.ui.[entityTable],
            messageConfigMaps: {
                global: GLOBAL_CONFIGS,
                [entity]: [ENTITY]_CONFIGS
            }
        });
    }

    // Required methods - implement these for your entity
    async fetchData() {
        // Convert jQuery promise to native promise for async/await compatibility
        const data = await new Promise((resolve, reject) => {
            [EntityApi].get[Entity]Data()
                .done(resolve)
                .fail(reject);
        });
        return data || [];
    }

    getTableHeaders() {
        // Return object with column headers using configs
        const columns = [ENTITY]_CONFIGS.ui.[entityTable].columns;
        return {
            column1: getLocalizedText({ message: columns.column1 }, 'message'),
            column2: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.dateModified }, 'message')
        };
    }

    buildTableRows(data) {
        // Return HTML string for table rows
        return data.map(item => `
            <tr>
                <td>${escapeHtml(item.field1 || '')}</td>
                <td>${formatDate(item.updated_at || item.created_at)}</td>
            </tr>
        `).join('');
    }
}

// Create and export singleton instance
export const [entity]Table = new [Entity]Table();
```

#### Functional Approach (Simple Tables)

For simple tables that don't require complex state management, use the functional approach:

```javascript
/*
 * Simple Table Implementation using buildTableHtml helper
 * Best for straightforward display tables without complex interactions
 */
import { buildTableHtml, buildTableMessageConfig, initializeWETTable } from '../../core/helpers/table-helpers.js';
import { [EntityApi] } from '../../core/services/[entity]-api.js';
import { escapeHtml, formatDate } from '../../core/helpers/format-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { [ENTITY]_CONFIGS } from '../../core/config/[entity]-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

async function create[Entity]Table(containerId = '[entity]-table-container') {
    const $container = $(`#${containerId}`);

    try {
        // Show loading state
        $container.html('<div class="text-center"><p>Loading...</p></div>');

        // Fetch data
        const data = await new Promise((resolve, reject) => {
            [EntityApi].get[Entity]Data()
                .done(resolve)
                .fail(reject);
        });

        if (!data || data.length === 0) {
            // Show empty state
            const entityTerm = getLocalizedText({ message: [ENTITY]_CONFIGS.ui.[entityTable].entityTerms }, 'message');
            const emptyMessage = getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.messageTemplates.emptyTable }, 'message')
                .replace('{entity}', entityTerm);
            $container.html(`<div class="alert alert-info"><p>${emptyMessage}</p></div>`);
            return;
        }

        // Build table using helper function
        const headers = {
            column1: getLocalizedText({ message: [ENTITY]_CONFIGS.ui.[entityTable].columns.column1 }, 'message'),
            column2: getLocalizedText({ message: GLOBAL_CONFIGS.ui.table.columns.dateModified }, 'message')
        };

        const headerCells = Object.values(headers).map(header =>
            `<th scope="col">${header}</th>`
        ).join('');

        const tableRows = data.map(item => `
            <tr>
                <td>${escapeHtml(item.field1 || '')}</td>
                <td>${formatDate(item.updated_at || item.created_at)}</td>
            </tr>
        `).join('');

        const messageConfig = buildTableMessageConfig([ENTITY]_CONFIGS.ui.[entityTable].entityTerms);

        // Use buildTableHtml helper for standard table structure
        const tableHtml = buildTableHtml({
            tableId: '[entity]-table',
            headerCells,
            tableRows,
            messageConfig,
            tableOptions: {
                // Custom WET-BOEW options if needed
                pageLength: 25
            }
        });

        $container.html(tableHtml);

        // Initialize WET-BOEW
        await initializeWETTable('[entity]-table');

    } catch (error) {
        console.error('Failed to create [entity] table:', error);
        $container.html('<div class="alert alert-danger"><p>Failed to load data. Please try again.</p></div>');
    }
}

// Export the function
export { create[Entity]Table };
```

#### When to Use Each Approach

**Use Object-Oriented Approach When**:
- Table has complex state management
- Requires editing/saving functionality
- Has multiple behavior modes
- Needs lifecycle management
- Benefits from inheritance and polymorphism

**Use Functional Approach When**:
- Simple display-only table
- No state management needed
- One-time table creation
- Custom implementation requirements
- Minimal overhead preferred

#### Usage Examples

**Object-Oriented Approach Usage**:
```javascript
// Standard usage with default configuration
await [entity]Table.create();

// Custom configuration
await [entity]Table.create({
    containerId: 'custom-container-id',
    tableId: 'custom-table-id',
    userInfo: userData  // Additional data if needed
});
```

**Functional Approach Usage**:
```javascript
// Simple function call
await create[Entity]Table();

// With custom container
await create[Entity]Table('custom-container-id');
```

#### Available Helper Functions

**Base Table Classes** (`base-table.js`):
- `BaseTable` - Abstract base class with common functionality
- `ReadOnlyTable` - For display-only tables (extends BaseTable)
- `BulkCheckboxTable` - For bulk operations (extends BaseTable)
- Provides standardized loading, error handling, and WET-BOEW integration

**Table Helper Functions** (`table-helpers.js`):
- `initializeWETTable(tableId)` - WET-BOEW DataTables initialization
- `buildTableMessageConfig(entityTerms)` - Localized message configuration
- `buildTableHtml(options)` - Complete table HTML structure (functional approach)
- `buildEmptyTableMessage(entityTerms)` - Empty state message

**Format Helpers** (`format-helpers.js`):
- `escapeHtml(text)` - HTML escaping for security
- `formatDate(dateString, format)` - Date formatting with localization
- `getRoleDisplayName(roleName)` - Role display names
- `formatStatusDisplay(isActive, statusLabels)` - Status display formatting
- `formatNotAvailable()` - "Not available" text formatting

**Message Helpers** (`message-helpers.js`):
- `showMessage(containerId, configPath, messageKey, configMaps, placeholders)` - Standardized message display
- Automatic scrolling and message management

**Domain-Specific Helpers**:
- Place in `pages/[domain]/shared/` for cross-page utilities within a domain
- Use `core/helpers/` only for truly global utilities

#### Table Implementation Types

**Read-Only Display Tables** (most common):
- **Object-Oriented**: Extend `ReadOnlyTable` for complex display logic
- **Functional**: Use `buildTableHtml()` for simple display tables
- Focus on data presentation and user experience
- No editing functionality required

**Editable Management Tables** (less common):
- **Always Object-Oriented**: Extend `BulkCheckboxTable` for state management
- Add change tracking (`this.hasChanges`, `this.trackChanges()`)
- Implement bulk operations and save functionality
- Include form validation and error handling

#### Naming Conventions

**File Naming**:
- **Simple tables** → `[entity]-table.js` (e.g., `staff-table.js`, `test-types-table.js`)
- **Complex managers** → `[entity]-table-manager.js` (e.g., `requisition-table-manager.js`)

**Class Naming**:
- **Simple tables** → `[Entity]Table` class, `[entity]Table` instance
- **Complex managers** → `[Entity]TableManager` class, `[entity]TableManager` instance

**When to use "manager" suffix**:
- Has complex state management
- Has editing/saving functionality
- Has multiple behavior modes
- Otherwise, use simple "table" suffix

#### Best Practices & Security

**Architecture Decisions**:
1. **Choose the right approach**: Object-oriented for complex tables, functional for simple ones
2. **Use helper functions**: Don't duplicate HTML escaping, date formatting, or WET-BOEW initialization
3. **Follow naming conventions**: Use appropriate table vs manager naming
4. **Leverage configuration**: Store entity-specific terms in config files

**Implementation Standards**:
5. **Handle empty states**: Always provide meaningful empty table messages using `buildEmptyTableMessage()`
6. **Error handling**: Use `showMessage()` for consistent GC-compliant error display
7. **Security**: Always use `escapeHtml()` for user-generated content (required even for internal apps)
8. **Async pattern**: Use async/await with Promise wrapper for API calls (never use jQuery promises directly)

**Standard Flow**:
- **Object-Oriented**: `create()` → Loading state → `fetchData()` → `buildTable()` → WET-BOEW init → Error handling
- **Functional**: Loading state → Data fetch → `buildTableHtml()` → WET-BOEW init → Error handling

**Performance Considerations**:
- Use functional approach for simple tables to reduce overhead
- Use object-oriented approach when you need state management or complex interactions
- Both approaches provide the same WET-BOEW integration and user experience