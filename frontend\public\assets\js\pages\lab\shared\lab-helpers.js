/*
 * Lab Helpers
 * Common utilities for lab-related data processing and display
 * Shared between lab-related pages only
 */
import { formatNotAvailable } from '../../../core/helpers/format-helpers.js';
import { TestApi } from '../../../core/services/test-api.js';

// Extract lab ID from user data
export function getLabId(userData) {
    return userData.lab || null;
}

// Check if user has lab assignment
export function hasLabAssignment(userData) {
    return !!(userData.lab && userData.lab_roles && userData.lab_roles.length > 0);
}

// Get lab role information for current lab (optimized - used by other functions)
export function getCurrentLabRole(userData) {
    if (!hasLabAssignment(userData)) {
        return null;
    }

    return userData.lab_roles.find(roleData =>
        roleData.lab === userData.lab
    ) || null;
}

// Extract lab name from user data with fallback handling (now uses getCurrentLabRole)
export function getLabName(userData, fallback = null) {
    const notAvailableText = fallback || formatNotAvailable();

    if (!hasLabAssignment(userData)) {
        return notAvailableText;
    }

    const currentLabRole = getCurrentLabRole(userData);
    if (currentLabRole && currentLabRole.lab_name) {
        return currentLabRole.lab_name;
    }

    // Fallback to generic lab name if lab_name not available
    return userData.lab ? `Laboratory ${userData.lab}` : notAvailableText;
}

// Format lab role display text (role name + lab name)
export function formatLabRoleDisplay(roleData, getRoleDisplayName) {
    if (!roleData) {
        return formatNotAvailable();
    }
    
    const roleName = roleData.role || roleData;
    const labName = roleData.lab_name || null;
    
    // Get localized role display name
    const displayName = getRoleDisplayName(roleName);
    
    // Create role text with lab information if available
    let roleText = displayName;
    if (labName) {
        roleText += ` (${labName})`;
    }
    
    return roleText;
}

// Common test types API call with lab filtering (shared between read-only and management tables)
export async function fetchTestTypesForLab(userInfo, additionalParams = {}) {
    const labId = getLabId(userInfo);
    if (!labId) {
        console.warn('No lab ID available for test types filtering');
        throw new Error('No lab ID available');
    }

    const params = { lab_id: labId, ...additionalParams };

    // TestApi.listTestTypes() returns a jQuery promise, convert to native promise
    const testTypes = await new Promise((resolve, reject) => {
        TestApi.listTestTypes(params)
            .done(resolve)
            .fail(reject);
    });

    return testTypes || [];
}
