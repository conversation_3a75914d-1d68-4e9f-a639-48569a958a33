/*
 * Base API Service
 * Handles common logic for all API requests, including authentication, error handling, etc.
 */
import { GLOBAL_CONFIGS } from '../config/global-configs.js';
import { getLocalizedText } from '../i18n/i18n-helpers.js';
import { redirectToPageByKey } from '../helpers/navigation-helpers.js';

// API Service base class for HTTP requests with authentication and error handling
export class ApiBase {
    static get(endpoint, queryParams = {}) {
        return this._request('GET', endpoint, queryParams);
    }

    static post(endpoint, data = {}) {
        return this._request('POST', endpoint, null, data);
    }

    static put(endpoint, data = {}) {
        return this._request('PUT', endpoint, null, data);
    }

    static delete(endpoint) {
        return this._request('DELETE', endpoint);
    }

    // Core request handler with authentication and error handling
    static _request(method, endpoint, queryParams = null, body = null) {
        const token = localStorage.getItem("access_token");
        if (!token) {
            const deferred = $.Deferred();
            const authErrorMessage = getLocalizedText(GLOBAL_CONFIGS.messages.warnings.api.authentication, 'message');
            deferred.reject({
                status: 401,
                responseJSON: { detail: authErrorMessage }
            });
            return deferred.promise();
        }

        // Build request URL
        let url = `${GLOBAL_CONFIGS.api.baseUrl}${endpoint}`;
        let requestData = null;

        // Handle query parameters
        if (queryParams && Object.keys(queryParams).length > 0) {
            // Special case for status parameter with pre-formatted multi-status values
            if (Object.keys(queryParams).length === 1 && queryParams.status &&
                typeof queryParams.status === 'string' && queryParams.status.includes('&status=')) {
                // Directly append the complete status string to URL (without encoding)
                url += `?status=${queryParams.status}`;
            } else {
                // Normal handling for other query parameters
                requestData = queryParams;
            }
        }

        // Prepare request options
        const ajaxOptions = {
            url: url,
            method: method,
            headers: {
                'Authorization': `Bearer ${token}`
            },
            dataType: 'json'
        };

        // Add request body for POST/PUT requests
        if (body && (method === 'POST' || method === 'PUT')) {
            ajaxOptions.contentType = 'application/json';
            ajaxOptions.data = JSON.stringify(body);
        } else if (requestData && method === 'GET') {
            ajaxOptions.data = requestData;
        }

        // Create a deferred object to handle custom error processing
        const deferred = $.Deferred();

        $.ajax(ajaxOptions)
            .done(function(data) {
                deferred.resolve(data);
            })
            .fail(function(xhr, textStatus) {
                // Handle 401 authentication errors
                if (xhr.status === 401) {
                    console.error('Authentication failed. Redirecting to login...');
                    localStorage.removeItem('access_token');

                    // Redirect to login page
                    redirectToPageByKey('login');

                    const sessionExpiredMessage = getLocalizedText(GLOBAL_CONFIGS.messages.warnings.api.authentication, 'message');
                    deferred.reject({
                        status: 401,
                        responseJSON: { detail: sessionExpiredMessage }
                    });
                    return;
                }

                // Handle other errors
                let errorDetail = `HTTP error! status: ${xhr.status}`;
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorDetail = xhr.responseJSON.detail;
                }

                console.error(`API request failed: ${method} ${endpoint}`, {
                    status: xhr.status,
                    error: errorDetail,
                    textStatus: textStatus
                });

                deferred.reject(xhr);
            });

        return deferred.promise();
    }
}
