/*
 * View Requisitions Page JavaScript
 * Handles initialization of requisition tables for all user roles
 * Key difference from home.js: ALL roles see both open and closed requisitions
 */
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { requisitionTableManager } from './requisition-table-manager.js';
import { getLabId } from '../lab/shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    requisition: REQUISITION_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeViewRequisitionsPage
};

// Initialize view requisitions page
async function initializeViewRequisitionsPage(userInfo) {
    try {
        await initializeAllRequisitionTables(userInfo);
    } catch (error) {
        console.error('View requisitions page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize both open and closed requisition tables for all roles
async function initializeAllRequisitionTables(userInfo) {
    // Create table configurations
    const openRequisitionsConfig = {
        ...REQUISITION_CONFIGS.ui.table.open,
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    const closedRequisitionsConfig = {
        ...REQUISITION_CONFIGS.ui.table.closed,
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    // Key difference from home.js: ALL roles see both open and closed requisitions
    const $openSection = $('#open-requisitions-section');
    const $closedSection = $('#closed-requisitions-section');

    if ($openSection.length && $closedSection.length) {
        // Handle lab staff who need lab ID
        if ((userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN || userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL) && !getLabId(userInfo)) {
            // Handle missing lab error
            showMessage($openSection, 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
            showMessage($closedSection, 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
            return;
        }

        // Show both sections for all roles
        $openSection.prop('hidden', false).attr('aria-hidden', 'false');
        $closedSection.prop('hidden', false).attr('aria-hidden', 'false');

        // Create both tables using Promise.all for better async handling
        await Promise.all([
            requisitionTableManager.create(openRequisitionsConfig),
            requisitionTableManager.create(closedRequisitionsConfig)
        ]);

        console.log('Both requisition tables initialized successfully');
    }
}
